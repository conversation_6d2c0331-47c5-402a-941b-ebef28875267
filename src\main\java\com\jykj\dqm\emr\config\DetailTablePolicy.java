package com.jykj.dqm.emr.config;

import cn.hutool.core.util.ObjectUtil;
import com.deepoove.poi.policy.DynamicTableRenderPolicy;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblWidth;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STTblWidth;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 自定义动态表格
 * 重写render方法
 */
public class DetailTablePolicy extends DynamicTableRenderPolicy {
    @Override
    public void render(XWPFTable table, Object data) throws Exception {
        if (null == data) return;
        CTTblPr tblPr = table.getCTTbl().getTblPr();
        tblPr.getTblW().setType(STTblWidth.DXA);
        //8503DXA=15CM
        tblPr.getTblW().setW(new BigInteger("8503"));
        List<Map<String, Object>> targetRowData = (List<Map<String, Object>>) data;
        if (ObjectUtil.isNotEmpty(targetRowData) && targetRowData.size() > 0) {
            table.removeRow(0);
            //循环插入行数据
            List<String> list = new ArrayList<>();
            for (int i = 0; i < targetRowData.size(); i++) {
                //第一行是标题行
                XWPFTableRow xwpfTableRow = table.insertNewTableRow(i);
                //循环列 row-cell
                int index = 0;
                for (Map.Entry vo : targetRowData.get(i).entrySet()) {
                    XWPFTableCell cell = xwpfTableRow.createCell();
                    //if (i == 0) {
                    //    // 背景色
                    //    cell.setColor("00ff00");
                    //}
                    CTTcPr tcpr = cell.getCTTc().addNewTcPr();
                    CTTblWidth cellw = tcpr.addNewTcW();
                    cellw.setType(STTblWidth.DXA);
                    cellw.setW(BigInteger.valueOf(360 * 5));
                    XWPFParagraph p = cell.getParagraphs().get(0);
                    // 文字靠左位置
                    p.setAlignment(ParagraphAlignment.LEFT);
                    // create a run
                    XWPFRun r = p.createRun();
                    r.setFontFamily("宋体");
                    r.setFontSize(11);
                    //单元格赋值
                    r.setText(vo.getValue().toString());
                    if (i == 0) {
                        // set font to bold
                        r.setBold(true);
                    } else {
                        if (index == 0) {
                            list.add(vo.getValue().toString());
                        }
                    }
                    index++;
                }
            }
            mergeCells(table, list);
        }
    }

    /**
     * 合并单元格（第一列）
     *
     * @param table XWPFTable
     * @param list  系统明细
     */
    private void mergeCells(XWPFTable table, List<String> list) {
        if (list.size() == 0) {
            return;
        }
        Map<String, Integer> map = new LinkedHashMap<>();
        for (String name : list) {
            map.put(name, map.getOrDefault(name, 0) + 1);
        }
        int startIndex = 1;
        int endIndex = 0;
        for (Integer value : map.values()) {
            endIndex += value;
            mergeColumn(table, 0, startIndex, endIndex);
            startIndex = value + 1;

        }
    }

    /**
     * 合并行
     *
     * @param tableRow XWPFTableRow
     * @param fromCell 开始合并的单元格
     * @param toCell   合并到哪一个单元格
     */
    public synchronized void mergeCellsHorizontal(XWPFTableRow tableRow, int fromCell, int toCell) {
        for (int cellIndex = fromCell; cellIndex <= toCell; cellIndex++) {
            XWPFTableCell cell = tableRow.getCell(cellIndex);
            if (cellIndex == fromCell) {
                // 改列的单元格开始的地方
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
            } else {
                // 该列单元格结束的地方
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
            }
        }
    }

    /**
     * 合并行
     *
     * @param table    要合并单元格的表格
     * @param row      要合并哪一行的单元格
     * @param fromCell 开始合并的单元格
     * @param toCell   合并到哪一个单元格
     */
    public void mergeCellsHorizontal(XWPFTable table, int row, int fromCell, int toCell) {
        for (int cellIndex = fromCell; cellIndex <= toCell; cellIndex++) {
            XWPFTableCell cell = table.getRow(row).getCell(cellIndex);
            if (cellIndex == fromCell) {
                // The first merged cell is set with RESTART merge value
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
            } else {
                // Cells which join (merge) the first one, are set with CONTINUE
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
            }
        }
    }

    /**
     * 合并列
     *
     * @param table   表格
     * @param col     要合并的列
     * @param fromRow 开始的行
     * @param toRow   结束的行
     */
    public void mergeColumn(XWPFTable table, int col, int fromRow, int toRow) {
        for (int rowIndex = fromRow; rowIndex <= toRow; rowIndex++) {
            XWPFTableCell cell = table.getRow(rowIndex).getCell(col);
            if (rowIndex == fromRow) {
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.RESTART);
            } else {
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.CONTINUE);
            }
        }
    }
}