package com.jykj.dqm.emr.utils;

import cn.hutool.core.util.StrUtil;
import com.jykj.dqm.common.Constant;
import com.jykj.dqm.emr.entity.DataDictionaryDirectoryConfiguration;
import com.jykj.dqm.emr.entity.DocumentDirectoryConfiguration;
import com.jykj.dqm.utils.StringUtil;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 电子病历的一些公共方法
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/4/14 10:14:29
 */
public class CommonUtils {
    /**
     * 按照目录编码+目录名称分组，编码要去掉_后的
     *
     * @param documentDirectoryConfigurations List<DocumentDirectoryConfiguration>
     * @return 分组后的文档目录
     * <AUTHOR>
     */
    public static Map<String, List<DocumentDirectoryConfiguration>> getGroupDocumentDirectoryByCodeAndName(List<DocumentDirectoryConfiguration> documentDirectoryConfigurations) {
        Map<String, List<DocumentDirectoryConfiguration>> collect = documentDirectoryConfigurations.stream().collect(Collectors.groupingBy(item -> {
                    String value = item.getDirectoryCode();
                    String name = item.getDirectoryName();
                    if (value.contains("_")) {
                        value = value.substring(0, value.indexOf("_"));
                    }
                    if (name.contains("_")) {
                        name = name.substring(0, name.indexOf("_"));
                    }
                    return value + name;
                }
        ));
        return collect;
    }

    /**
     * 按照目录编码，编码要去掉_后的
     *
     * @param documentDirectoryConfigurations List<DocumentDirectoryConfiguration>
     * @return 分组后的文档目录
     * <AUTHOR>
     */
    public static Map<String, List<DocumentDirectoryConfiguration>> getGroupDocumentDirectoryByCode(List<DocumentDirectoryConfiguration> documentDirectoryConfigurations) {
        Map<String, List<DocumentDirectoryConfiguration>> collect = documentDirectoryConfigurations.stream().collect(Collectors.groupingBy(item -> {
                    String value = item.getDirectoryCode();
                    if (value.contains("_")) {
                        return value.substring(0, value.indexOf("_"));
                    }
                    return value;
                }
        ));
        return collect;
    }

    public static String getGroupDocumentDirectoryCodeNameKey(DocumentDirectoryConfiguration documentDirectoryConfiguration) {
        String value = documentDirectoryConfiguration.getDirectoryCode();
        String name = documentDirectoryConfiguration.getDirectoryName();
        if (value.contains("_")) {
            value = value.substring(0, value.indexOf("_"));
        }
        if (name.contains("_")) {
            name = name.substring(0, name.indexOf("_"));
        }
        return value + name;
    }

    public static String getGroupDocumentDirectoryCodeKey(DocumentDirectoryConfiguration documentDirectoryConfiguration) {
        String value = documentDirectoryConfiguration.getDirectoryCode();
        if (value.contains("_")) {
            return value.substring(0, value.indexOf("_"));
        }
        return value;
    }


    public static boolean needAddRuleType2(Map<String, List<DocumentDirectoryConfiguration>> groupDocumentDirectoryByCode, DocumentDirectoryConfiguration documentDirectoryConfiguration) {
        String directoryCode = documentDirectoryConfiguration.getDirectoryCode();
        String[] array = directoryCode.split("\\.");
        int num = 0;
        if (array.length == 3) {
            if (array[2].contains("_")) {
                num = Integer.parseInt(array[2].substring(0, array[2].indexOf("_")));
            } else {
                num = Integer.parseInt(array[2]);
            }
        }
        List<DocumentDirectoryConfiguration> documentDirectoryConfigurations = groupDocumentDirectoryByCode.get(getGroupDocumentDirectoryCodeKey(documentDirectoryConfiguration));
        if (directoryCode.contains("_")) {
            return true;
        }
        if (documentDirectoryConfigurations.size() > 1 && num >= 5) {
            Set<String> names = new HashSet<>();
            for (DocumentDirectoryConfiguration directoryConfiguration : documentDirectoryConfigurations) {
                names.add(directoryConfiguration.getDirectoryName());
                if (directoryConfiguration.getDirectoryCode().contains("_")) {
                    return true;
                }
            }
            if (names.size() == documentDirectoryConfigurations.size()) {
                return false;
            }
            return true;
        }
        return false;
    }


    public static boolean needAddRuleType(Map<String, List<DocumentDirectoryConfiguration>> groupDocumentDirectoryByCode, DocumentDirectoryConfiguration documentDirectoryConfiguration) {
        String directoryCode = documentDirectoryConfiguration.getDirectoryCode();
        List<DocumentDirectoryConfiguration> documentDirectoryConfigurations = groupDocumentDirectoryByCode.get(getGroupDocumentDirectoryCodeKey(documentDirectoryConfiguration));
        if (directoryCode.contains("_") || documentDirectoryConfigurations.stream().anyMatch(dc -> dc.getDirectoryCode().contains("_"))) {
            return true;
        }
        //07.01.5血液准备_完整性
        //07.01.5血液准备_整合性

        //05.01.4标本处理
        //05.01.4标本处理_病理
        Set<String> names = documentDirectoryConfigurations.stream().map(dc -> dc.getDirectoryName()).collect(Collectors.toSet());
        return names.size() != documentDirectoryConfigurations.size();
    }

    public static boolean needAddRuleType5(Map<String, List<DocumentDirectoryConfiguration>> groupDocumentDirectoryByCode, DocumentDirectoryConfiguration documentDirectoryConfiguration) {
        String directoryCode = documentDirectoryConfiguration.getDirectoryCode();
        List<DocumentDirectoryConfiguration> documentDirectoryConfigurations = groupDocumentDirectoryByCode.get(getGroupDocumentDirectoryCodeKey(documentDirectoryConfiguration));
        if (directoryCode.contains("_") || documentDirectoryConfigurations.stream().anyMatch(dc -> dc.getDirectoryCode().contains("_"))) {
            return true;
        }

        if (groupDocumentDirectoryByCode.size() <= 1) {
            return false;
        }
        //07.01.5血液准备_完整性
        //07.01.5血液准备_整合性

        //05.01.4标本处理
        //05.01.4标本处理_病理
        Set<String> names = documentDirectoryConfigurations.stream().map(dc -> dc.getDirectoryName()).collect(Collectors.toSet());
        return names.size() != documentDirectoryConfigurations.size();
    }

    public static boolean needAddRuleType3(Map<String, List<DocumentDirectoryConfiguration>> groupDocumentDirectoryByCode, DocumentDirectoryConfiguration documentDirectoryConfiguration) {
        String directoryCode = documentDirectoryConfiguration.getDirectoryCode();
        List<DocumentDirectoryConfiguration> documentDirectoryConfigurations = groupDocumentDirectoryByCode.get(getGroupDocumentDirectoryCodeKey(documentDirectoryConfiguration));
        if (directoryCode.contains("_")) {
            return true;
        }
        if (documentDirectoryConfigurations.size() > 1) {
            Set<String> names = new HashSet<>();
            for (DocumentDirectoryConfiguration directoryConfiguration : documentDirectoryConfigurations) {
                names.add(directoryConfiguration.getDirectoryName());
                if (directoryConfiguration.getDirectoryCode().contains("_")) {
                    return true;
                }
            }
            if (names.size() == documentDirectoryConfigurations.size()) {
                return false;
            }
            return true;
        }
        return false;
    }

    /**
     * 根据长度拆分字符串，并使用换行连接
     * String.format() 方法用于创建格式化的字符串，根据不同的格式化参数，规定字符串的表现形式。对于`String.format("%1$-6s", sub);`这个特定的例子，它的作用如下：
     * - %: 这是格式化字符串的标识。
     * - 1$: 这指定第一个参数将被格式化并放置在这个位置。在这种情况下，数字 1 表示要格式化的是第一个参数，`$` 是参数索引的终止符。
     * - -: 这是左对齐修饰符。默认情况下，格式化的字符串是右对齐的。如果添加了 -，则会将文本左对齐。
     * - 6: 这指定了最小的宽度为 6 个字符。如果提供的字符串小于 6 个字符，格式化的结果将用空格填充到至少 6 个字符宽。
     * - s: 这表明待格式化的参数应该被视为一个字符串。
     * 整体来说，这个格式化字符串的意思是，它将接受一个字符串参数 sub，并确保格式化后的字符串是左对齐，并至少包含 6 个字符的宽度。如果 sub 长度小于 6，右边会用空格填充直到达到 6 个字符的长度。如果 sub 长度大于或等于 6，则直接返回 sub。
     *
     * @param str
     * @param len
     * @return
     */
    public static String splitStringByLength(String str, int len) {
        if (StrUtil.isBlank(str)) {
            return "";
        }
        if (str.length() <= len || str.contains("\n") || str.contains(Constant.LINE_SEPARATOR)) {
            return str;
        }
        String[] strings = Stream.iterate(0, i -> i + len)
                .limit((str.length() + len - 1) / len)
                .map(j -> {
                    String sub = str.substring(j, Math.min(j + len, str.length()));
                    // 如果子字符串长度小于 len，用空格补全长度
                    if (sub.length() < len) {
                        int spaceNum = (len + sub.length()) / 2;
                        sub = String.format("%1$" + spaceNum + "s", sub);
                    }
                    return sub;
                })
                .toArray(String[]::new);
        return String.join(Constant.LINE_SEPARATOR, strings);
    }


    public static void sortByDirectoryCode(List<? extends DataDictionaryDirectoryConfiguration> list) {
        list.sort((o1, o2) -> {
            String directoryCode1 = o1.getDirectoryCode();
            String directoryCode2 = o2.getDirectoryCode();
            directoryCode1 = directoryCode1.endsWith(".") ? directoryCode1.substring(0, directoryCode1.length() - 1) + "-" : directoryCode1;
            directoryCode2 = directoryCode2.endsWith(".") ? directoryCode2.substring(0, directoryCode2.length() - 1) + "-" : directoryCode2;
            String[] code1array = directoryCode1.split("\\.");
            String[] code2array = directoryCode2.split("\\.");
            int max = Math.max(code1array.length, code2array.length);
            int value1;
            int value2;
            for (int i = 0; i < max; i++) {
                //最后一位比较字符串，因为情况特殊
                String s1 = StringUtil.getStringValue(code1array, i);
                String s2 = StringUtil.getStringValue(code2array, i);
                if (!StringUtil.isInteger(s1)) {
                    s2 = concatArraysAsStr(code2array, i);
                    return s1.compareTo(s2);
                }
                if (!StringUtil.isInteger(s2)) {
                    s1 = concatArraysAsStr(code1array, i);
                    return s1.compareTo(s2);
                }
                value1 = Integer.parseInt(s1);
                value2 = Integer.parseInt(s2);
                if (value1 != value2) {
                    return value1 - value2;
                }
            }
            return 0;
        });
    }

    //拼接数字中剩下的字典，并用.分割
    private static String concatArraysAsStr(String[] code1array, int i) {
        if (i >= code1array.length) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (int j = i; j < code1array.length; j++) {
            sb.append(code1array[j]);
            sb.append(".");
        }
        return sb.toString().substring(0, sb.length() - 1);
    }
}
