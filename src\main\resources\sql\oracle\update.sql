-- 新增电子病历评级文档导出
INSERT INTO sys_config
(module_id, config_code, config_name, description, config_value, config_type, sort_code, deleted_flag, create_by, create_time, update_by, update_time, sys_id, web_show, module_name)
VALUES('3', 'hospital.code', '医院代码', '医院代码', '452183688', '1', 1, '0', '52', sysdate, '52', NULL, 'DQM', 'Y', '系统');
INSERT INTO sys_config
( module_id, config_code, config_name, description, config_value, config_type, sort_code, deleted_flag, create_by, create_time, update_by, update_time, sys_id, web_show, module_name)
VALUES('3', 'hospital.name', '医院名称', '医院名称', '南充市中心医院', '1', 1, '0', '52', sysdate, '52', NULL, 'DQM', 'Y', '系统');

-- 为DQM_METADATA_DATASOURCE表添加NEED_CROSS_QUERY字段
-- Oracle版本
ALTER TABLE DQM_METADATA_DATASOURCE ADD NEED_CROSS_QUERY NUMBER(1) DEFAULT 0 NULL;
COMMENT ON COLUMN DQM_METADATA_DATASOURCE.NEED_CROSS_QUERY IS '是否需要跨库关联查询(0:否,1:是)';