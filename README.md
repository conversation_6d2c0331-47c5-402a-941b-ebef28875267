# EMRrate-Management-System

电子病历评级文档管理系统

## 功能说明

### 文档转换功能
系统支持多种文档格式转换，便于文档的处理和展示：

1. **Word转HTML**: 将Word文档转换为HTML格式，支持在线预览
2. **Word转PDF**: 将Word文档转换为PDF格式
3. **Word转OFD**: 将Word文档转换为OFD(开放版式文档)格式

### 加密工具类
系统提供统一的加密解密工具类，便于数据安全管理：

1. **HashCryptoFactory**: 提供哈希加密工厂，支持多种哈希算法（SM3和MD5），用于密码等敏感信息的单向加密
2. **SM3Util**: 提供国密SM3哈希算法实现，用于密码等敏感信息的单向加密
3. **MD5Util**: 提供MD5哈希算法实现，向下兼容旧系统
4. **SymmetricCryptoFactory**: 提供对称加密工厂，支持多种对称加密算法（SM4和AES），用于数据的可逆加密和解密
5. **加密用途**: 主要用于数据库密码等敏感信息的加密存储和解密使用

### 数据源跨库关联查询功能
系统支持配置数据源是否需要跨库关联查询功能：

1. **功能说明**: 当数据源配置为需要跨库关联查询时，系统会自动配置Presto相关环境
2. **配置流程**: 
   - 在数据源中设置"是否需要跨库关联查询"为"是"
   - 系统自动在/home/<USER>/pw/pw.env文件中添加或修改数据源密码配置
   - 系统自动在/home/<USER>/catalog/目录下创建或修改对应的properties文件
   - 系统自动执行Docker相关命令重新构建和启动Presto容器
3. **支持的数据库类型**: MySQL、Oracle、SQL Server、PostgreSQL等
4. **全局配置项**: 
   - 在application.yml中可以配置是否开启自动配置跨库catalog功能
   - 默认关闭，需要手动开启才会执行自动配置
   ```yaml
   # 跨库查询配置
   cross-query:
     auto-catalog-config: false  # 是否开启自动配置跨库catalog，默认关闭
     presto-docker-path: /home/<USER>
     presto-env-password: Jykj1994@  # Presto环境密码，用于加密env.encrypted文件
   ```

### 配置加密算法
系统支持通过配置文件指定默认加密算法，可在application.yml或各环境配置文件中设置：

```yaml
# 加密算法配置
crypto:
  hash-algorithm: SM3  # 可选值: SM3, MD5 - 用于哈希加密（如密码哈希）
  symmetric-algorithm: SM4  # 可选值: SM4, AES - 用于对称加密（如数据加密）
```

### 使用方法

#### Word转OFD
```java
// 示例：将Word文档转换为OFD格式
try {
    String wordFilePath = "文件路径/文档.docx";
    String ofdOutputPath = "文件路径/文档.ofd";
    WordUtil.docxToOFD(wordFilePath, ofdOutputPath);
} catch (Exception e) {
    e.printStackTrace();
}
```

#### 哈希加密（单向加密）
```java
// 示例：使用HashCryptoFactory进行哈希加密
String content = "需要加密的内容";
// 使用默认哈希加密算法(配置文件中指定)加密
String encrypted = HashCryptoFactory.encrypt(content);

// 使用指定算法加密
CryptoUtil md5Crypto = HashCryptoFactory.getCrypto("MD5");
String md5Encrypted = md5Crypto.encrypt(content);

CryptoUtil sm3Crypto = HashCryptoFactory.getCrypto("SM3");
String sm3Encrypted = sm3Crypto.encrypt(content);
```

#### 对称加密和解密（可逆加密）
```java
// 示例：使用SymmetricCryptoFactory进行对称加密和解密
String content = "需要加密的内容";

// 使用默认对称加密算法(配置文件中指定)加密
String encrypted = SymmetricCryptoFactory.encrypt(content);
// 解密
String decrypted = SymmetricCryptoFactory.decrypt(encrypted);

// 使用指定算法加密解密
SymmetricCryptoUtil aesCrypto = SymmetricCryptoFactory.getCrypto("AES");
String aesEncrypted = aesCrypto.encrypt(content);
String aesDecrypted = aesCrypto.decrypt(aesEncrypted);

SymmetricCryptoUtil sm4Crypto = SymmetricCryptoFactory.getCrypto("SM4");
String sm4Encrypted = sm4Crypto.encrypt(content);
String sm4Decrypted = sm4Crypto.decrypt(sm4Encrypted);

// 判断是否是加密内容
boolean isEncrypted = SymmetricCryptoFactory.isEncrypted(someString);
```