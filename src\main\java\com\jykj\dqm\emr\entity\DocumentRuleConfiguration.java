package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 文档规则配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/22 15:50:15
 */
@ApiModel(description = "文档规则配置")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_EMR_DOCUMENT_RULE_CONFIGURATION")
public class DocumentRuleConfiguration extends UserAndTimeEntity implements Serializable {
    /**
     * ID
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "ID")
    private String id;

    @TableField(value = "PROJECT_ID")
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 目录编码
     */
    @TableField(value = "DIRECTORY_CODE")
    @ApiModelProperty(value = "目录编码")
    private String directoryCode;

    /**
     * 目录名称
     */
    @TableField(value = "DIRECTORY_NAME")
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
     * 规则类型
     */
    @TableField(value = "EMR_RULE_TYPE")
    @ApiModelProperty(value = "规则类型")
    private String emrRuleType;

    /**
     * 要求项目
     */
    @TableField(value = "REQUIRED_PROJECT")
    @ApiModelProperty(value = "要求项目")
    private String requiredProject;

    /**
     * 医院项目
     */
    @TableField(value = "HOSPITAL_PROJECT")
    @ApiModelProperty(value = "医院项目")
    private String hospitalProject;

    /**
     * 表与字段类型：0：选择表选择字段；1：自定义
     */
    @TableField(value = "TABLE_AND_FILED_TYPE")
    @ApiModelProperty(value = "表与字段类型：0：选择表选择字段；1：自定义")
    private String tableAndFiledType = "0";

    /**
     * 表/视图名1
     */
    @TableField(value = "STRUCTURE_NAME1")
    @ApiModelProperty(value = "表/视图名1")
    private String structureName1;


    /**
     * 表与字段名1
     */
    @TableField(value = "TABLE_FIELD_NAME1")
    @ApiModelProperty(value = "表与字段名1")
    private String tableFieldName1;

    /**
     * 表/视图名2
     */
    @TableField(value = "STRUCTURE_NAME2")
    @ApiModelProperty(value = "表/视图名2")
    private String structureName2;

    /**
     * 表与字段名2（字典表字段、对照表字段；一致性+整合性特有）
     */
    @TableField(value = "TABLE_FIELD_NAME2")
    @ApiModelProperty(value = "表与字段名2（字典表字段、对照表字段；一致性+整合性特有）")
    private String tableFieldName2;

    /**
     * 记录数SQL
     */
    @TableField(value = "RECORDS_SQL")
    @ApiModelProperty(value = "记录数SQL")
    private String recordsSql;

    /**
     * 记录数
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "记录数")
    private Long recordsNum;

    /**
     * 满足条件记录数SQL（完整记录/符合逻辑关系时间项/对照项可匹配数/与字典内容一致）
     */
    @TableField(value = "CONDITIONAL_RECORDS_SQL")
    @ApiModelProperty(value = "满足条件记录数SQL（完整记录/符合逻辑关系时间项/对照项可匹配数/与字典内容一致）")
    private String conditionalRecordsSql;

    /**
     * 满足条件记录数（完整记录/符合逻辑关系时间项/对照项可匹配数/与字典内容一致）
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "满足条件记录数（完整记录/符合逻辑关系时间项/对照项可匹配数/与字典内容一致）")
    private Long conditionalRecordsNum;

    /**
     * 表头名称1（及时性+整合性）
     */
    @TableField(value = "HEADER_NAME1")
    @ApiModelProperty(value = "表头名称1（及时性+整合性）")
    private String headerName1;

    /**
     * 表头名称2（整合性）
     */
    @TableField(value = "HEADER_NAME2")
    @ApiModelProperty(value = "表头名称2（整合性）")
    private String headerName2;

    /**
     * 数据源ID
     */
    @TableField(value = "DATA_SOURCE_ID")
    @ApiModelProperty(value = "数据源ID")
    private String dataSourceId;

    /**
     * 数据源ID2
     */
    @TableField(value = "DATA_SOURCE_ID2")
    @ApiModelProperty(value = "数据源ID2")
    private String dataSourceId2;

    /**
     * 是否跨库查询0：否 ，1：是
     */
    @TableField(value = "WHETHER_CROSS_DB_QUERY")
    @ApiModelProperty(value = "是否跨库查询 0：否 ，1：是")
    private String whetherCrossDbQuery = "0";

    /**
     * 跨库查询数据源ID
     */
    @TableField(value = "CROSS_DB_QUERY_DATA_SOURCE_ID")
    @ApiModelProperty(value = "跨库查询数据源ID")
    private String crossDbQueryDataSourceId;

    /**
     * 需要统计0：是 ，1：否
     */
    @TableField(value = "NEED_STATISTICS")
    @ApiModelProperty(value = "需要统计0：是 ，1：否")
    private String needStatistics = "0";

    public DocumentRuleConfiguration(String requiredProject, String hospitalProject, String needStatistics) {
        this.requiredProject = requiredProject;
        this.hospitalProject = hospitalProject;
        this.needStatistics = needStatistics;
    }

    public DocumentRuleConfiguration(String id, String requiredProject, String hospitalProject, String needStatistics) {
        this.id = id;
        this.requiredProject = requiredProject;
        this.hospitalProject = hospitalProject;
        this.needStatistics = needStatistics;
    }

    private static final long serialVersionUID = 1L;
}