package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 文档目录配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/21 15:48:52
 */
@ApiModel(description = "文档目录配置")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "DQM_EMR_DOCUMENT_DIRECTORY_CONFIGURATION")
public class DocumentDirectoryConfiguration extends UserAndTimeEntity implements Serializable {
    /**
     * ID
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 序号
     */
    @TableField(value = "SERIAL_NUM")
    @ApiModelProperty(value = "序号")
    private String serialNum;

    /**
     * 目录名称
     */
    @TableField(value = "DIRECTORY_NAME")
    @ApiModelProperty(value = "目录名称")
    @NotBlank
    private String directoryName;

    /**
     * 目录编码
     */
    @TableField(value = "DIRECTORY_CODE")
    @ApiModelProperty(value = "目录编码")
    @NotBlank
    private String directoryCode;

    /**
     * 等级关联
     */
    @TableField(value = "LEVEL_CODE")
    @ApiModelProperty(value = "等级关联")
    private String levelCode;

    /**
     * 规则类型（1：一致性、2：完整性、3：整合性、4：及时性）
     */
    @TableField(value = "EMR_RULE_TYPE")
    @ApiModelProperty(value = "规则类型（1：一致性、2：完整性、3：整合性、4：及时性），直接填汉字")
    private String emrRuleType;

    /**
     * 父ID
     */
    @TableField(value = "PARENT_ID")
    @ApiModelProperty(value = "父ID")
    private String parentId;

    /**
     * 完整目录名称
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "完整目录名称")
    private String fullDirectoryName;

    /**
     * 任务状态
     */
    @ApiModelProperty(value = "任务状态:0-未分配，1-已分配，2-已完成")
    @TableField(exist = false)
    private String taskStatus;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField(exist = false)
    private String personInCharge;

    private static final long serialVersionUID = 1L;
}