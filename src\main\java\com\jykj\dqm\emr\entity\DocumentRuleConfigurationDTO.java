package com.jykj.dqm.emr.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 文档规则配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/22 15:50:15
 */
@ApiModel(description = "文档规则配置")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DocumentRuleConfigurationDTO extends UserAndTimeEntity implements Serializable {
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 目录编码
     */
    @TableField(value = "DIRECTORY_CODE")
    @ApiModelProperty(value = "目录编码")
    private String directoryCode;

    /**
     * 目录名称
     */
    @TableField(value = "DIRECTORY_NAME")
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
     * 规则类型
     */
    @TableField(value = "EMR_RULE_TYPE")
    @ApiModelProperty(value = "规则类型")
    private String emrRuleType;

    /**
     * 表头名称1（及时性+整合性）
     */
    @TableField(value = "HEADER_NAME1")
    @ApiModelProperty(value = "表头名称1（及时性+整合性）")
    private String headerName1;

    /**
     * 表头名称2（整合性）
     */
    @TableField(value = "HEADER_NAME2")
    @ApiModelProperty(value = "表头名称2（整合性）")
    private String headerName2;

    /**
     * 数据源ID
     */
    @TableField(value = "DATA_SOURCE_ID")
    @ApiModelProperty(value = "数据源ID")
    private String dataSourceId;

    /**
     * 数据源ID2
     */
    @TableField(value = "DATA_SOURCE_ID2")
    @ApiModelProperty(value = "数据源ID2")
    private String dataSourceId2;

    @TableField(exist = false)
    @ApiModelProperty(value = "要求项目配置")
    private List<DocumentRuleConfiguration> documentRuleConfigurationList;

    private static final long serialVersionUID = 1L;
}