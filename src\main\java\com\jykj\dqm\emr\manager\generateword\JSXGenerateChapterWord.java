package com.jykj.dqm.emr.manager.generateword;

import com.jykj.dqm.emr.entity.DocumentDirectoryConfiguration;
import com.jykj.dqm.emr.entity.DocumentRuleConfiguration;
import com.jykj.dqm.exception.BusinessException;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 及时性
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/28 14:09:35
 */
@Component
public class JSXGenerateChapterWord extends GenerateChapterWord {
    @Override
    public void dealEachDoc(List<DocumentRuleConfiguration> documentRuleConfigurations, DocumentDirectoryConfiguration documentDirectoryConfiguration, String dataStartTime, String dataEndTime, String recordId) {
        throw new BusinessException("目前不支持及时性");
    }

    @Override
    public RuleTypeEnum gainRuleRuleType() {
        return RuleTypeEnum.JSX;
    }
}
