package com.jykj.dqm.emr.manager.generateword;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepoove.poi.data.Pictures;
import com.jykj.dqm.emr.entity.DocumentDirectoryConfiguration;
import com.jykj.dqm.emr.entity.DocumentRuleConfiguration;
import com.jykj.dqm.emr.entity.DocumentRuleSqlExecRecord;
import com.jykj.dqm.emr.manager.DbQueryNewUtil;
import com.jykj.dqm.emr.manager.WordUtils;
import com.jykj.dqm.emr.utils.PathNameUtils;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.metadata.entity.MetadataDatasource;
import com.jykj.dqm.utils.SymmetricCryptoFactory;
import com.jykj.dqm.utils.DateTimeUtils;
import com.jykj.dqm.utils.NumberMyUtil;
import com.jykj.dqm.utils.RedisUtil;
import com.jykj.dqm.utils.SystemUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 整合性
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/28 14:09:35
 */
@Slf4j
@Component
public class ZHXGenerateChapterWord extends GenerateChapterWord {
    @Autowired
    private DbQueryNewUtil dbQueryUtil;

    @Override
    public void dealEachDoc(List<DocumentRuleConfiguration> documentRuleConfigurations,
            DocumentDirectoryConfiguration documentDirectoryConfiguration, String dataStartTime, String dataEndTime,
            String recordId) {
        if (documentRuleConfigurations.size() == 0) {
            return;
        }
        List<Map<String, Object>> table1Data = new ArrayList<>();
        // 标题行
        DocumentRuleConfiguration documentRuleConfiguration1 = documentRuleConfigurations.get(0);
        Map<String, Object> table1Map = new LinkedHashMap<>();
        table1Map.put("one", "要求项目");
        table1Map.put("two", "医院项目");
        // 动态添加表头
        String headerName1 = documentRuleConfiguration1.getHeaderName1();
        String headerName2 = documentRuleConfiguration1.getHeaderName2();
        table1Map.put("three", headerName1 + "记录表与字段名");
        table1Map.put("four", headerName2 + "记录表与字段名");
        table1Data.add(table1Map);
        List<Map<String, Object>> table2Data = new ArrayList<>();
        // 标题行
        Map<String, Object> table2Map = new LinkedHashMap<>();
        table2Map.put("one", "序号");
        table2Map.put("two", "医院项目");
        // 动态添加表头
        table2Map.put("three", headerName1 + "记录数");
        table2Map.put("four", headerName2 + "可关联对照记录数");
        table2Map.put("five", "整合性比例I");
        table2Data.add(table2Map);
        // 文件名
        String docNameBase = null;
        // 带路径的文件名
        String docNamePathBase = null;
        List<Map<String, Object>> sqlData = new ArrayList();
        Map<String, Object> sqlmap;
        int countNum = 0;
        String recordsSql = "";
        String conditionalRecordsSql = "";
        long all = 0;
        String allRequiredProject = "";
        String picturePath;
        Map<String, Object> params = new LinkedHashMap<>();
        long match = 0;
        String rate = "";
        String path = SystemUtils.getFilePath() + "/word/" + recordId + "/";
        String dbType = "";
        String dbType2 = "";
        String dataSourceId = "";
        String dataSourceId2 = "";
        List<DocumentRuleSqlExecRecord> documentRuleSqlExecRecords = new ArrayList<>();
        String configValue = RedisUtil.getSysConfigValue("emr.word.haspicture", "N");
        for (DocumentRuleConfiguration documentRuleConfiguration : documentRuleConfigurations) {
            table1Map = new LinkedHashMap<>();
            table1Map.put("one", documentRuleConfiguration.getRequiredProject());
            table1Map.put("two", documentRuleConfiguration.getHospitalProject());
            table1Map.put("three", getTableAndFieldName(documentRuleConfiguration.getTableAndFiledType(),
                    documentRuleConfiguration.getStructureName1(), documentRuleConfiguration.getTableFieldName1()));
            table1Map.put("four",
                    "1".equals(documentRuleConfiguration.getNeedStatistics()) ? ""
                            : getTableAndFieldName(documentRuleConfiguration.getTableAndFiledType(),
                                    documentRuleConfiguration.getStructureName2(),
                                    documentRuleConfiguration.getTableFieldName2()));
            table1Data.add(table1Map);
            if (!"1".equals(documentRuleConfiguration.getNeedStatistics())) {
                if ("1".equals(documentRuleConfiguration.getWhetherCrossDbQuery())
                        && StrUtil.isBlank(documentRuleConfiguration.getCrossDbQueryDataSourceId())) {
                    throw new BusinessException("未配置数据源！！！");
                }
                if ("0".equals(documentRuleConfiguration.getWhetherCrossDbQuery())
                        &&
                        (StrUtil.isBlank(documentRuleConfiguration.getDataSourceId())
                                || StrUtil.isBlank(documentRuleConfiguration.getDataSourceId2()))) {
                    throw new BusinessException("未配置数据源！！！");
                }
                countNum++;
                // 连接数据源查询结果
                // DbQueryUtil dbQueryUtil = new DbQueryUtil();
                // 根据dataSourceId查询数据源信息
                dataSourceId = getDataSourceId(documentRuleConfiguration);
                MetadataDatasource metadataDatasource = RedisUtil.getMetadataDatasourceById(dataSourceId);
                metadataDatasource.setDatabasePwd(SymmetricCryptoFactory.decrypt(metadataDatasource.getDatabasePwd()));

                // 第一次进入
                dbType = metadataDatasource.getDatabaseType();
                if (docNameBase == null) {
                    docNameBase = PathNameUtils.getFileName(documentRuleConfiguration.getDirectoryCode(),
                            documentRuleConfiguration.getDirectoryName(), documentRuleConfiguration.getEmrRuleType());
                    docNamePathBase = path + docNameBase;
                }
                recordsSql = addTimeToSql(documentRuleConfiguration.getRecordsSql(), dbType, dataStartTime,
                        dataEndTime);
                all = dbQueryUtil.queryCount(metadataDatasource, recordsSql);
                // 生成总的图片
                if ("Y".equalsIgnoreCase(configValue)) {
                    picturePath = docNamePathBase + "All.png";
                    generatePicture(picturePath, recordsSql, metadataDatasource, all);
                    params.put("pictureAll", Pictures.ofLocal(picturePath).create());
                }

                dataSourceId2 = getDataSourceId2(documentRuleConfiguration);
                MetadataDatasource metadataDatasource2 = RedisUtil.getMetadataDatasourceById(dataSourceId2);
                metadataDatasource2
                        .setDatabasePwd(SymmetricCryptoFactory.decrypt(metadataDatasource2.getDatabasePwd()));
                dbType2 = metadataDatasource2.getDatabaseType();

                conditionalRecordsSql = addTimeToSql(documentRuleConfiguration.getConditionalRecordsSql(), dbType2,
                        dataStartTime, dataEndTime);
                match = dbQueryUtil.queryCount(metadataDatasource2, conditionalRecordsSql);
                rate = NumberMyUtil.calculationRatio(all, match);

                buildSqlExecRecord(documentRuleConfiguration, (int) all, (int) match, documentRuleSqlExecRecords,
                        recordId);

                table2Map = new LinkedHashMap<>();

                table2Map.put("one", countNum);
                table2Map.put("two", documentRuleConfiguration.getHospitalProject());
                table2Map.put("three", all);
                table2Map.put("four", match);
                table2Map.put("five", rate);
                table2Data.add(table2Map);
                if (countNum == 1) {
                    allRequiredProject = documentRuleConfiguration.getRequiredProject();
                } else {
                    allRequiredProject = allRequiredProject + "、" + documentRuleConfiguration.getRequiredProject();
                }
                // SQL
                sqlmap = new LinkedHashMap();
                sqlmap.put("name", "L" + countNum);
                sqlmap.put("sql", conditionalRecordsSql);

                if ("Y".equalsIgnoreCase(configValue)) {
                    picturePath = docNamePathBase + "L" + countNum + ".png";
                    generatePicture(picturePath, conditionalRecordsSql, metadataDatasource, match);
                    sqlmap.put("picture", Pictures.ofLocal(picturePath).create());
                }
                sqlData.add(sqlmap);
            }
        }
        WordUtils wordUtils = new WordUtils();
        // 生成表1和表2
        wordUtils.generateTable(table1Data, docNamePathBase + "table1");
        wordUtils.generateTable(table2Data, docNamePathBase + "table2");

        params.put("allRecord", all);
        // sql数据
        params.put("recordsSql", recordsSql);
        params.put("sqlData", sqlData);
        params.put("dbType", dbType);
        params.put("dbType2", dbType2);// 目前没改
        // 其他数据（每个规则对应的名称，级别，时间范围，统计项目数 n（可以从data2的size-1获得，及时性为固定值1））
        int projectNum = table2Data.size() - 1;
        params.put("nNum", projectNum);
        params.put("dataStartTime", dataStartTime);// 转换为2022-04-01 yyyy-MM-dd
        params.put("dataEndTime", dataEndTime);// 转换为2022-04-01 yyyy-MM-dd
        params.put("levelCode", documentDirectoryConfiguration.getLevelCode());

        // 完整性特殊 T1-T6
        params.put("tRange", "T1-T" + projectNum);
        params.put("sqlAll", recordsSql);

        // 处理公式
        dealCalculationFormula(table2Data, params, projectNum, documentDirectoryConfiguration, recordId);

        params.put("table1", docNamePathBase + "table1");
        params.put("table2", docNamePathBase + "table2");
        params.put("directoryName", documentDirectoryConfiguration.getDirectoryName());
        params.put("allRequiredProject", allRequiredProject);
        params.put("headerName1", headerName1);
        params.put("headerName2", headerName2);

        params.put("monthNum", DateTimeUtils.getMonthBetween(dataStartTime, dataEndTime));
        // 增加标记，用于导出时，补充默认信息 private void updateDoc(List<DocumentExportRecordRuleDetail>
        // list, String filePath) throws IOException
        params.put("problemDataRemarks1", "{{problemDataRemarks1}}");
        params.put("problemDataRemarks2", "{{problemDataRemarks2}}");
        params.put("problemDataRemarks3", "{{problemDataRemarks3}}");
        params.put("problemDataRemarks4", "{{problemDataRemarks4}}");
        params.put("remarks1", "{{?remarks}}");
        params.put("remarks2", "{{/remarks}}");
        wordUtils.generateWordForEachChapter(docNamePathBase + "_pre", RuleTypeEnum.ZHX.getValue(), params);
        // 删除是为了处理重新生成的场景
        documentRuleSqlExecRecordService.remove(Wrappers.<DocumentRuleSqlExecRecord>lambdaQuery()
                .eq(DocumentRuleSqlExecRecord::getExportRecordId, recordId)
                .eq(DocumentRuleSqlExecRecord::getEmrRuleType, documentDirectoryConfiguration.getEmrRuleType())
                .eq(DocumentRuleSqlExecRecord::getDirectoryCode, documentDirectoryConfiguration.getDirectoryCode())
                .eq(DocumentRuleSqlExecRecord::getDirectoryName, documentDirectoryConfiguration.getDirectoryName()));
        documentRuleSqlExecRecordService.saveBatch(documentRuleSqlExecRecords);
    }

    private static void buildSqlExecRecord(DocumentRuleConfiguration documentRuleConfiguration, int all, int match,
            List<DocumentRuleSqlExecRecord> documentRuleSqlExecRecords, String recordId) {
        DocumentRuleSqlExecRecord documentRuleSqlExecRecord = new DocumentRuleSqlExecRecord();
        documentRuleSqlExecRecord.setExportRecordId(recordId);
        documentRuleSqlExecRecord.setEmrRuleType(documentRuleConfiguration.getEmrRuleType());
        documentRuleSqlExecRecord.setDirectoryCode(documentRuleConfiguration.getDirectoryCode());
        documentRuleSqlExecRecord.setDirectoryName(documentRuleConfiguration.getDirectoryName());
        documentRuleSqlExecRecord.setRequiredProject(documentRuleConfiguration.getRequiredProject());
        documentRuleSqlExecRecord.setRecordsNum(all);
        documentRuleSqlExecRecord.setConditionalRecordsNum(match);
        documentRuleSqlExecRecords.add(documentRuleSqlExecRecord);
    }

    private void dealCalculationFormula(List<Map<String, Object>> table2Data, Map<String, Object> params,
            int projectNum, DocumentDirectoryConfiguration documentDirectoryConfiguration, String recordId) {
        // 完整性系数 = ( N1/T1 + N2/T2 + N3/T3 + N4/T4 + N5/T5 + N6/T6 ) / 6
        // = ( 1.0 + 1.0 + 1.0 + 1.0 + 1.0 + 1.0 ) / 6
        // = 1.0
        StringBuilder calculationFormula1 = new StringBuilder();
        StringBuilder calculationFormula2 = new StringBuilder();
        calculationFormula1.append("(");
        calculationFormula2.append("(");
        double allCount = 0.0;
        double eachCountDouble;
        // 第一行是标题
        for (int i = 1; i <= projectNum; i++) {
            if (i != 1) {
                calculationFormula1.append("+");
                calculationFormula2.append("+");
            }
            String eachCount = (String) table2Data.get(i).get("five");
            eachCountDouble = Double.parseDouble(eachCount);
            allCount += eachCountDouble;
            calculationFormula1.append("L" + i).append("/").append("T" + i);
            calculationFormula2.append(eachCount);
        }

        calculationFormula1.append(")").append("/").append("n");
        calculationFormula2.append(")").append("/").append(projectNum);
        params.put("calculationFormula1", calculationFormula1.toString());
        params.put("calculationFormula2", calculationFormula2.toString());

        String ratio = NumberMyUtil.calculationRatio(allCount, projectNum);
        params.put("calculationFormulaResult", ratio);
        // 记录告警
        recordRedisExceptionInfo(recordId, documentDirectoryConfiguration.getDirectoryCode(),
                documentDirectoryConfiguration.getDirectoryName(), RuleTypeEnum.ZHX.getValue(), ratio);
    }

    @Override
    public RuleTypeEnum gainRuleRuleType() {
        return RuleTypeEnum.ZHX;
    }
}
