package com.jykj.dqm.metadata.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.common.UploadDriverFile;
import com.jykj.dqm.config.CrossQueryConfig;
import com.jykj.dqm.config.commondb.DbConfig;
import com.jykj.dqm.config.commondb.DbConnectionPoolUtil;
import com.jykj.dqm.emr.manager.DbQueryNewUtil;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.metadata.dao.MetadataDatasourceMapper;
import com.jykj.dqm.metadata.dao.TaskGroupSubtasksMapper;
import com.jykj.dqm.metadata.entity.MetadataDatasource;
import com.jykj.dqm.metadata.entity.MetadataDatasourceDeleteDTO;
import com.jykj.dqm.metadata.entity.MetadataDatasourceQueryDTO;
import com.jykj.dqm.metadata.entity.MetadataDatasourceVO;
import com.jykj.dqm.metadata.entity.MetadataStructureChooseResult;
import com.jykj.dqm.metadata.entity.MetadataStructureDetail;
import com.jykj.dqm.metadata.entity.MetadataStructureInfo;
import com.jykj.dqm.metadata.entity.TaskGroupSubtasks;
import com.jykj.dqm.metadata.service.MetadataDatasourceService;
import com.jykj.dqm.metadata.service.MetadataStructureChooseResultService;
import com.jykj.dqm.metadata.service.MetadataStructureDetailService;
import com.jykj.dqm.metadata.service.MetadataStructureInfoService;
import com.jykj.dqm.metadata.service.TaskGroupSubtasksService;
import com.jykj.dqm.utils.MapperUtils;
import com.jykj.dqm.utils.PageUtil;
import com.jykj.dqm.utils.RedisUtil;
import com.jykj.dqm.utils.SymmetricCryptoFactory;
import com.jykj.dqm.utils.SystemUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 数据源配置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/8/22 11:35:34
 */
@Slf4j
@Service
public class MetadataDatasourceServiceImpl extends ServiceImpl<MetadataDatasourceMapper, MetadataDatasource>
        implements MetadataDatasourceService {
    @Autowired
    MetadataDatasourceMapper dqmMetadataDatasourceMapper;

    @Autowired
    MetadataStructureInfoService metadataStructureInfoService;

    @Autowired
    MetadataStructureDetailService metadataStructureDetailService;

    @Autowired
    TaskGroupSubtasksService dqmTaskGroupSubtasksService;

    @Autowired
    TaskGroupSubtasksMapper dqmTaskGroupSubtasksMapper;

    @Autowired
    UploadDriverFile uploadDriverFile;

    @Autowired
    private DbQueryNewUtil dbQueryUtil;

    @Autowired
    private MetadataStructureChooseResultService metadataStructureChooseResultService;

    @Autowired
    private CrossQueryConfig crossQueryConfig;

    @Override
    public R selectMetadataDatasourceList(MetadataDatasourceQueryDTO dqmMetadataDatasource) {
        PageHelper.startPage(dqmMetadataDatasource.getPageNum(), dqmMetadataDatasource.getPageSize());
        // 增加是否已关联任务选项 关联任务状态:0:全部；1：关联；2：未关联
        List<MetadataDatasource> dqmMetadataDatasources = dqmMetadataDatasourceMapper
                .getDatasourceByParams(dqmMetadataDatasource);
        PageInfo<MetadataDatasource> pageInfo = new PageInfo<>(dqmMetadataDatasources);
        PageInfo<MetadataDatasourceVO> copy = PageUtil.pageInfoCopy(pageInfo, MetadataDatasourceVO.class);
        for (MetadataDatasourceVO metadataDatasource : copy.getList()) {
            long count = metadataStructureChooseResultService.count(new QueryWrapper<MetadataStructureChooseResult>()
                    .eq("DATA_SOURCE_ID", metadataDatasource.getDataSourceId()));
            metadataDatasource.setConfigNum(count);
        }
        return RUtil.success(copy);
    }

    @Override
    public R addDatasource(MetadataDatasource dqmMetadataDatasource) {
        dqmMetadataDatasource.setOperationPerson("管理员");
        String realPassword = dqmMetadataDatasource.getDatabasePwd();
        dqmMetadataDatasource.setDatabasePwd(SymmetricCryptoFactory.encrypt(realPassword));

        // 如果needCrossQuery为null，设置默认值为0
        if (dqmMetadataDatasource.getNeedCrossQuery() == null) {
            dqmMetadataDatasource.setNeedCrossQuery(0);
        }

        dqmMetadataDatasourceMapper.insert(dqmMetadataDatasource);

        // 异步处理跨库关联查询配置
        handleCrossQueryConfigAsync(dqmMetadataDatasource, realPassword);
        //handleCrossQueryConfigSync(dqmMetadataDatasource, realPassword);

        RedisUtil.deleteByNamespacePrefix("EMRM:Metadata:");
        return RUtil.success("新增成功！");
    }

    @Override
    public R updateDatasource(MetadataDatasource dqmMetadataDatasource) {
        dqmMetadataDatasource.setUpdateTime(new Date());

        // 获取原始数据源信息
        MetadataDatasource dbMd = dqmMetadataDatasourceMapper.selectById(dqmMetadataDatasource.getDataSourceId());
        String realPassword = dqmMetadataDatasource.getDatabasePwd();

        // 判断密码是否需要加密
        if (!SymmetricCryptoFactory.isEncrypted(dqmMetadataDatasource.getDatabasePwd())) {
            dqmMetadataDatasource
                    .setDatabasePwd(SymmetricCryptoFactory.encrypt(dqmMetadataDatasource.getDatabasePwd()));
        } else {
            // 后面需要原密码，需要解密
            realPassword = SymmetricCryptoFactory.decrypt(dqmMetadataDatasource.getDatabasePwd());
        }

        // 如果needCrossQuery为null，设置默认值为0
        if (dqmMetadataDatasource.getNeedCrossQuery() == null) {
            dqmMetadataDatasource.setNeedCrossQuery(0);
        }

        dqmMetadataDatasourceMapper.updateById(dqmMetadataDatasource);

        // 异步处理跨库关联查询配置
        handleCrossQueryConfigAsync(dqmMetadataDatasource, realPassword);
        //handleCrossQueryConfigSync(dqmMetadataDatasource, realPassword);

        RedisUtil.deleteByNamespacePrefix("EMRM:Metadata:");
        DbConfig dbConfig = MapperUtils.INSTANCE.map(DbConfig.class, dbMd);
        try {
            DbConnectionPoolUtil.closeOnePool(dbConfig);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return RUtil.success("更新成功！");
    }

    /**
     * 异步处理跨库关联查询配置
     *
     * @param datasource  数据源信息
     * @param rawPassword 原始密码
     */
    private void handleCrossQueryConfigAsync(MetadataDatasource datasource, String rawPassword) {
        if (!crossQueryConfig.isAutoCatalogConfig()) {
            return;
        }

        try {
            // 重新生成所有跨库关联查询配置
            regenerateCrossQueryConfig(datasource, rawPassword);

            // 执行Docker命令
            executeDockerCommands();

            log.info("异步处理跨库关联查询配置完成");
        } catch (Exception e) {
            log.error("异步处理跨库关联查询配置失败", e);
        }

        log.info("已启动异步处理跨库关联查询配置任务");
    }

    /**
     * 同步处理跨库关联查询配置
     *
     * @param datasource  数据源信息
     * @param rawPassword 原始密码
     */
    private void handleCrossQueryConfigSync(MetadataDatasource datasource, String rawPassword) {
        if (!crossQueryConfig.isAutoCatalogConfig()) {
            return;
        }
        try {
            // 重新生成所有跨库关联查询配置
            regenerateCrossQueryConfig(datasource, rawPassword);

            // 执行Docker命令
            executeDockerCommands();

            log.info("异步处理跨库关联查询配置完成");
        } catch (Exception e) {
            log.error("异步处理跨库关联查询配置失败", e);
        }

        log.info("已启动异步处理跨库关联查询配置任务");
    }

    /**
     * 重新生成所有跨库关联查询配置
     *
     * @param currentDatasource 当前操作的数据源
     * @param currentPassword   当前数据源的密码
     * @throws IOException
     */
    private void regenerateCrossQueryConfig(MetadataDatasource currentDatasource, String currentPassword)
            throws IOException {
        // 查询所有需要跨库关联查询的数据源
        List<MetadataDatasource> crossQueryDatasources = dqmMetadataDatasourceMapper.selectList(
                new QueryWrapper<MetadataDatasource>().eq("NEED_CROSS_QUERY", 1));

        // 创建密码配置映射
        Map<String, String> passwordConfigMap = new HashMap<>();

        // 处理当前数据源
        if (currentDatasource != null && currentDatasource.getNeedCrossQuery() == 1) {
            String configKey = currentDatasource.getDataSourceName().toUpperCase() + "_" +
                    currentDatasource.getDatabaseType().toUpperCase() + "_PASSWORD";
            passwordConfigMap.put(configKey, currentPassword);

            // 创建或更新properties文件
            createOrUpdatePropertiesFile(currentDatasource);
        }

        // 处理其他跨库数据源
        for (MetadataDatasource ds : crossQueryDatasources) {
            // 跳过当前数据源，因为已经处理过了
            if (currentDatasource != null && ds.getDataSourceId().equals(currentDatasource.getDataSourceId())) {
                continue;
            }

            String configKey = ds.getDataSourceName().toUpperCase() + "_" +
                    ds.getDatabaseType().toUpperCase() + "_PASSWORD";

            // 解密密码
            String password = SymmetricCryptoFactory.decrypt(ds.getDatabasePwd());
            passwordConfigMap.put(configKey, password);

            // 创建或更新properties文件
            createOrUpdatePropertiesFile(ds);
        }

        // 生成新的pw.env文件
        String pwEnvPath = crossQueryConfig.getPrestoDockerPath() + "/pw/pw.env";
        File pwEnvFile = new File(pwEnvPath);

        // 确保目录存在
        if (!pwEnvFile.getParentFile().exists()) {
            pwEnvFile.getParentFile().mkdirs();
        }

        // 写入新的pw.env文件
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(pwEnvFile))) {
            for (Map.Entry<String, String> entry : passwordConfigMap.entrySet()) {
                writer.write(entry.getKey() + "=" + entry.getValue());
                writer.newLine();
            }
        }

        log.info("成功重新生成pw.env文件，包含{}个数据源配置", passwordConfigMap.size());
    }

    /**
     * 创建或更新数据源对应的properties文件
     * 
     * @param datasource 数据源信息
     * @throws IOException
     */
    private void createOrUpdatePropertiesFile(MetadataDatasource datasource) throws IOException {
        // 如果是PRESTO类型，不生成配置文件
        if ("PRESTO".equalsIgnoreCase(datasource.getDatabaseType())) {
            log.info("PRESTO类型数据源不生成配置文件: {}", datasource.getDataSourceName());
            return;
        }

        String catalogDir = crossQueryConfig.getPrestoDockerPath() + "/catalog/";
        String fileName = datasource.getDataSourceName().toLowerCase() + ".properties";
        String filePath = catalogDir + fileName;

        // 确保目录存在
        File catalogDirFile = new File(catalogDir);
        if (!catalogDirFile.exists()) {
            catalogDirFile.mkdirs();
        }

        // 根据数据库类型生成不同的配置内容
        StringBuilder content = new StringBuilder();
        String dbType = datasource.getDatabaseType().toLowerCase();
        String passwordEnvVar = "${ENV:" + datasource.getDataSourceName().toUpperCase() + "_" +
                datasource.getDatabaseType().toUpperCase() + "_PASSWORD}";

        if ("oracle".equalsIgnoreCase(dbType)) {
            // 处理Oracle URL - 将/SID格式转换为:SID格式
            String originalUrl = datasource.getDatabaseUrl();
            String processedUrl = originalUrl;

            // 处理URL格式
            if (originalUrl.contains("@")) {
                // 提取@后面的部分
                String prefix = originalUrl.substring(0, originalUrl.indexOf("@"));
                String hostPortSid = originalUrl.substring(originalUrl.indexOf("@") + 1);

                // 将/SID格式转换为:SID格式
                if (hostPortSid.contains("/")) {
                    hostPortSid = hostPortSid.replace("/", ":");
                }

                // 移除可能存在的//前缀
                if (hostPortSid.startsWith("//")) {
                    hostPortSid = hostPortSid.substring(2);
                }

                // 重新组合URL，在connection-url中添加用户名和密码
                processedUrl = prefix + datasource.getDatabaseUser() + "/" + passwordEnvVar + "@"
                        + hostPortSid;
                log.info("Oracle URL处理: {} -> {}", originalUrl, processedUrl);
            } else {
                log.warn("Oracle URL格式不符合预期: {}", originalUrl);
            }

            content.append("connector.name=oracle\n");
            content.append("connection-url=").append(processedUrl).append("\n");
            content.append("connection-user=").append(datasource.getDatabaseUser()).append("\n");
            content.append("connection-password=").append(passwordEnvVar).append("\n");
        } else if ("mysql".equalsIgnoreCase(dbType) || "mariadb".equalsIgnoreCase(dbType)) {
            // 处理MySQL/MariaDB URL
            String connectionUrl = processConnectionUrl(datasource.getDatabaseUrl(), dbType);

            content.append("connector.name=mysql\n");
            content.append("connection-url=").append(connectionUrl).append("\n");
            content.append("connection-user=").append(datasource.getDatabaseUser()).append("\n");
            content.append("connection-password=").append(passwordEnvVar).append("\n");
        } else if ("sqlserver".equalsIgnoreCase(dbType)) {
            // 处理SQL Server URL
            String connectionUrl = processConnectionUrl(datasource.getDatabaseUrl(), dbType);

            content.append("connector.name=sqlserver\n");
            content.append("connection-url=").append(connectionUrl).append("\n");
            content.append("connection-user=").append(datasource.getDatabaseUser()).append("\n");
            content.append("connection-password=").append(passwordEnvVar).append("\n");
        } else if ("postgresql".equalsIgnoreCase(dbType)) {
            // 处理PostgreSQL URL
            String connectionUrl = processConnectionUrl(datasource.getDatabaseUrl(), dbType);

            content.append("connector.name=postgresql\n");
            content.append("connection-url=").append(connectionUrl).append("\n");
            content.append("connection-user=").append(datasource.getDatabaseUser()).append("\n");
            content.append("connection-password=").append(passwordEnvVar).append("\n");
        } else {
            log.warn("不支持的数据库类型: {}, 数据源: {},请联系管理员配置", dbType, datasource.getDataSourceName());
            return;
        }

        // 写入文件
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
            writer.write(content.toString());
        }

        log.info("成功创建或更新properties文件: {}", filePath);
    }

    /**
     * 处理数据库连接URL，只保留到端口部分
     * 
     * @param originalUrl 原始数据库URL
     * @param dbType      数据库类型
     * @return 处理后的URL
     */
    private String processConnectionUrl(String originalUrl, String dbType) {
        if (originalUrl == null || originalUrl.isEmpty()) {
            return originalUrl;
        }

        String url = originalUrl;

        try {
            if ("mysql".equalsIgnoreCase(dbType) || "mariadb".equalsIgnoreCase(dbType)) {
                // 处理MySQL URL，格式如: ***********************************
                int lastSlashIndex = url.lastIndexOf("/");
                if (lastSlashIndex > 0 && url.indexOf(":", lastSlashIndex) < 0) {
                    // 如果最后一个斜杠后面没有冒号，说明是数据库名部分
                    url = url.substring(0, lastSlashIndex) + "?useSSL=false&autoReconnect=true&failOverReadOnly=false";
                    log.info("MySQL URL处理: {} -> {}", originalUrl, url);
                }
            } else if ("sqlserver".equalsIgnoreCase(dbType)) {
                // SQL Server URL通常格式为: *******************************************************
                int databaseParamIndex = url.toLowerCase().indexOf(";databasename=");
                if (databaseParamIndex > 0) {
                    url = url.substring(0, databaseParamIndex);
                    if(url.contains("jdbc:jtds:sqlserver")){
                        url = url.replace("jdbc:jtds:sqlserver", "jdbc:sqlserver");
                    }
                    log.info("SQL Server URL处理: {} -> {}", originalUrl, url);
                }
            } else if ("postgresql".equalsIgnoreCase(dbType)) {
                // PostgreSQL URL格式为: *********************************************
                // Presto要求保留数据库名
                log.info("PostgreSQL URL保持不变: {}", originalUrl);
            }
        } catch (Exception e) {
            log.warn("处理数据库URL时出错，将使用原始URL: {}", originalUrl, e);
            return originalUrl;
        }

        return url;
    }

    /**
     * 执行Docker相关命令
     *
     * @throws IOException
     */
    private void executeDockerCommands() throws IOException {
        ProcessBuilder pb;
        Process process;
        String prestoDockerPath = crossQueryConfig.getPrestoDockerPath();
        log.info("切换到指定目录：{}", prestoDockerPath + "/pw/");
        // 切换到指定目录
        pb = new ProcessBuilder("cd", prestoDockerPath + "/pw/");
        process = pb.start();
        try {
            process.waitFor();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("执行cd命令被中断", e);
        }

        // 删除旧镜像
        pb = new ProcessBuilder("docker", "rmi", "emrm-presto");
        log.info("删除旧镜像：{}", "emrm-presto");
        process = pb.start();
        try {
            process.waitFor();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("执行docker rmi命令被中断", e);
        }

        // 构建新镜像
        pb = new ProcessBuilder("docker", "build", "-t", "emrm-presto", ".");
        log.info("构建新镜像：{}", "emrm-presto");
        pb.directory(new File(prestoDockerPath + "/pw/"));
        process = pb.start();
        try {
            process.waitFor();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("执行docker build命令被中断", e);
        }

        // 执行加密脚本
        pb = new ProcessBuilder("./only_encrypt_file.sh", "-p", crossQueryConfig.getPrestoEnvPassword(), "pw.env",
                "env.encrypted");
        log.info("执行加密脚本");
        pb.directory(new File(prestoDockerPath + "/pw/"));
        process = pb.start();
        try {
            process.waitFor();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("执行加密脚本被中断", e);
        }

        // 删除原始pw.env文件，保证安全
        File pwEnvFile = new File(prestoDockerPath + "/pw/pw.env");
        if (pwEnvFile.exists() && pwEnvFile.delete()) {
            log.info("成功删除原始pw.env文件");
        } else {
            log.warn("删除原始pw.env文件失败");
        }

        // 启动Docker容器
        String[] dockerRunCommand = {
                "docker", "run", "-d",
                "--name", "emrm-presto",
                "-p", "8080:8080",
                "-e", "TZ=Asia/Shanghai",
                "-e", "PRESTO_ENV_PASSWORD=" + crossQueryConfig.getPrestoEnvPassword(),
                "-v", prestoDockerPath + "/config.properties:/opt/presto-server/etc/config.properties",
                "-v", prestoDockerPath + "/jvm.config:/opt/presto-server/etc/jvm.config",
                "-v", prestoDockerPath + "/catalog:/opt/presto-server/etc/catalog",
                "-v", prestoDockerPath + "/pw/env.encrypted:/opt/presto-server/etc/env.encrypted",
                "emrm-presto"
        };
        log.info("启动Docker容器：{}", Arrays.toString(dockerRunCommand));

        pb = new ProcessBuilder(dockerRunCommand);
        process = pb.start();
        try {
            process.waitFor();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("执行docker run命令被中断", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R deleteDatasource(MetadataDatasourceDeleteDTO dqmMetadataDatasource) {
        List<MetadataDatasource> dqmMetadataDatasources = dqmMetadataDatasourceMapper
                .selectBatchIds(dqmMetadataDatasource.getDataSourceIds());

        // 检查是否有需要跨库关联查询的数据源
        boolean hasCrossQueryDatasource = false;
        for (MetadataDatasource ds : dqmMetadataDatasources) {
            if (ds.getNeedCrossQuery() != null && ds.getNeedCrossQuery() == 1) {
                hasCrossQueryDatasource = true;
                break;
            }
        }

        dqmMetadataDatasourceMapper.deleteBatchIds(dqmMetadataDatasource.getDataSourceIds());

        // 删除上传文件
        Integer datasourceId;
        for (MetadataDatasource metadataDatasource : dqmMetadataDatasources) {
            datasourceId = metadataDatasource.getDataSourceId();
            if (StrUtil.isNotBlank(metadataDatasource.getDriverFiles())) {
                FileUtil.del(SystemUtils.getFilePath() + "/" + metadataDatasource.getDriverFiles());
            }
            // 删除采集的数据（根据数据源）
            metadataStructureInfoService
                    .remove(new QueryWrapper<MetadataStructureInfo>().eq("DATA_SOURCE_ID", datasourceId));
            metadataStructureDetailService
                    .remove(new QueryWrapper<MetadataStructureDetail>().eq("DATA_SOURCE_ID", datasourceId));
            // 删除数据源，同步删除包含当前数据源的子任务
            dqmTaskGroupSubtasksService.remove(new QueryWrapper<TaskGroupSubtasks>().eq("SUB_TASK_ID", datasourceId));

            // 如果是跨库关联查询的数据源，删除对应的properties文件
            if (metadataDatasource.getNeedCrossQuery() != null && metadataDatasource.getNeedCrossQuery() == 1) {
                String propertiesPath = crossQueryConfig.getPrestoDockerPath() + "/catalog/" +
                        metadataDatasource.getDataSourceName().toLowerCase() + ".properties";
                File propertiesFile = new File(propertiesPath);
                if (propertiesFile.exists() && propertiesFile.delete()) {
                    log.info("成功删除properties文件: {}", propertiesPath);
                }
            }
        }

        // 如果有跨库关联查询的数据源被删除，需要异步重新生成配置
        if (hasCrossQueryDatasource && crossQueryConfig.isAutoCatalogConfig()) {
            CompletableFuture.runAsync(() -> {
                try {
                    // 重新生成所有跨库关联查询配置
                    regenerateCrossQueryConfig(null, null);

                    // 执行Docker命令
                    executeDockerCommands();

                    log.info("删除数据源后异步处理跨库关联查询配置完成");
                } catch (Exception e) {
                    log.error("删除数据源后异步处理跨库关联查询配置失败", e);
                }
            });

            log.info("已启动删除数据源后的异步处理跨库关联查询配置任务");
        }

        try {
            for (MetadataDatasource metadataDatasource : dqmMetadataDatasources) {
                DbConfig dbConfig = MapperUtils.INSTANCE.map(DbConfig.class, metadataDatasource);
                DbConnectionPoolUtil.closeOnePool(dbConfig);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        RedisUtil.deleteByNamespacePrefix("EMRM:Metadata:");
        return RUtil.success("删除成功！");
    }

    @Override
    public R testDb(MetadataDatasource dqmMetadataDatasource) {
        try {
            List<Map<String, Object>> result = dbQueryUtil.query(dqmMetadataDatasource,
                    dqmMetadataDatasource.getTestsql());
            if (result != null) {
                return RUtil.success("测试连接成功！");
            } else {
                return RUtil.error("测试连接失败！");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RUtil.error("测试连接失败:" + e.getMessage());
        }
    }

    @Override
    public R uploadDriverFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return RUtil.error("文件不能为空！");
        }
        UploadDriverFile uploadDriverFile = SpringUtil.getBean(UploadDriverFile.class);
        double size = file.getSize() * 1.0 / 1024 / 1024;
        if (size > uploadDriverFile.getFileSize()) {
            return RUtil.error("文件大小不能超过10M！");
        }
        // 获取上传文件的文件名
        String oldName = file.getOriginalFilename();
        String filenameExtension = StringUtils.getFilenameExtension(oldName);
        if (!uploadDriverFile.getFileTypes().contains(filenameExtension)) {
            return RUtil.error("文件类型不支持！");
        }

        try {
            String path = SystemUtils.getFilePath();
            if (!FileUtil.exist(path)) {
                FileUtil.mkdir(path);
            }
            // 拼接成为新文件的路径
            String filePath = path + "/" + oldName;
            // 创建新文件对象 指定文件路径为拼接好的路径
            File newFile = new File(filePath);
            // 将前端传递过来的文件输送给新文件 这里需要抛出IO异常 throws IOException
            file.transferTo(newFile);
        } catch (IOException e) {
            throw new BusinessException("上传文件失败！" + e.getLocalizedMessage());
        }
        // 上传完成后将文件路径返回给前端用作图片回显或增加时的文件路径值等
        return RUtil.success("上传成功！");
    }
}
